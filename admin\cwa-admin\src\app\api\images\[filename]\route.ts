import { NextRequest, NextResponse } from 'next/server';

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:4000';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ filename: string }> }
) {
  try {
    const { filename } = await params;
    
    console.log('Image proxy request for:', filename);
    console.log('Backend URL:', `${BACKEND_URL}/images/file/${filename}`);

    // Validate filename to prevent directory traversal attacks
    if (!filename || filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return NextResponse.json(
        { status: 'error', message: 'Invalid filename' },
        { status: 400 }
      );
    }

    // Check if filename has valid image extension
    const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg|ico)$/i;
    if (!imageExtensions.test(filename)) {
      return NextResponse.json(
        { status: 'error', message: 'Invalid file type' },
        { status: 400 }
      );
    }

    // Fetch the image from the backend
    const response = await fetch(`${BACKEND_URL}/images/file/${filename}`, {
      method: 'GET',
      headers: {
        'Accept': 'image/*',
      },
    });

    if (!response.ok) {
      console.log('Image not found on backend:', filename);
      return NextResponse.json(
        { status: 'error', message: 'Image not found' },
        { status: 404 }
      );
    }

    // Get the image data
    const imageBuffer = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'image/jpeg';

    console.log('Successfully proxied image:', filename, 'Content-Type:', contentType);

    // Return the image with proper headers
    return new NextResponse(imageBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'X-Content-Type-Options': 'nosniff',
      },
    });

  } catch (error) {
    console.error('Error proxying image:', error);
    return NextResponse.json(
      { status: 'error', message: 'Error loading image' },
      { status: 500 }
    );
  }
}
